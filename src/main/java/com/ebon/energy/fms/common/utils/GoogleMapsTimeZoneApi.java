package com.ebon.energy.fms.common.utils;

import com.alibaba.fastjson.JSON;
import com.ebon.energy.fms.common.utils.google.GoogleMapsTimeZoneApiResponse;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.ZoneId;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

/**
 * Google Maps时区API客户端
 * API文档: https://developers.google.com/maps/documentation/timezone/
 */
public class GoogleMapsTimeZoneApi implements ITimeZoneApi {
    
    private static final String TIME_ZONE_API_BASE_URI = "https://maps.googleapis.com/maps/api/timezone/json";
    
    private final String apiKey;
    private final OkHttpClient httpClient;
    
    /**
     * 构造函数
     * 
     * @param apiKey Google Maps API密钥
     */
    public GoogleMapsTimeZoneApi(String apiKey) {
        this(apiKey, new OkHttpClient());
    }
    
    /**
     * 构造函数
     * 
     * @param apiKey Google Maps API密钥
     * @param httpClient 自定义的HTTP客户端
     */
    public GoogleMapsTimeZoneApi(String apiKey, OkHttpClient httpClient) {
        if (StringUtils.isBlank(apiKey)) {
            throw new IllegalArgumentException("API key cannot be null or empty");
        }
        if (httpClient == null) {
            throw new IllegalArgumentException("HTTP client cannot be null");
        }
        
        this.apiKey = apiKey;
        this.httpClient = httpClient;
    }
    
    /**
     * 异步获取指定经纬度的时区信息
     * 
     * @param lat 纬度
     * @param lon 经度
     * @return 返回Windows时区ID的CompletableFuture
     */
    @Override
    public CompletableFuture<String> getTimeZoneAsync(String lat, String lon) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getTimeZone(lat, lon);
            } catch (Exception e) {
                throw new CompletionException("Failed to get timezone for coordinates: " + lat + "," + lon, e);
            }
        });
    }
    
    /**
     * 同步获取指定经纬度的时区信息
     * 
     * @param lat 纬度
     * @param lon 经度
     * @return Windows时区ID
     * @throws IOException 网络请求异常
     */
    public String getTimeZone(String lat, String lon) throws IOException {
        if (StringUtils.isBlank(lat) || StringUtils.isBlank(lon)) {
            throw new IllegalArgumentException("Latitude and longitude cannot be null or empty");
        }
        
        String url = String.format("%s?location=%s,%s&timestamp=0&key=%s", 
                TIME_ZONE_API_BASE_URI, lat, lon, apiKey);
        
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("HTTP request failed with code: " + response.code());
            }
            
            String responseBody = response.body().string();
            GoogleMapsTimeZoneApiResponse apiResponse = JSON.parseObject(responseBody, GoogleMapsTimeZoneApiResponse.class);
            
            if (apiResponse == null) {
                throw new IOException("Failed to parse API response");
            }
            
            if (!"OK".equals(apiResponse.getStatus())) {
                String errorMsg = StringUtils.isNotBlank(apiResponse.getErrorMessage()) 
                        ? apiResponse.getErrorMessage() 
                        : "API returned status: " + apiResponse.getStatus();
                throw new IOException("Google Maps API error: " + errorMsg);
            }
            
            String windowsTimeZone = "";
            if (StringUtils.isNotBlank(apiResponse.getTimeZoneId())) {
                String ianaTimeZone = apiResponse.getTimeZoneId();
                windowsTimeZone = convertIanaToWindows(ianaTimeZone);
            }
            
            return windowsTimeZone;
        }
    }
    
    /**
     * 将IANA时区ID转换为Windows时区ID
     * 
     * @param ianaTimeZoneId IANA时区ID
     * @return Windows时区ID，如果转换失败返回空字符串
     */
    private String convertIanaToWindows(String ianaTimeZoneId) {
        if (StringUtils.isBlank(ianaTimeZoneId)) {
            return "";
        }
        
        try {
            // 使用现有的WindowsToZoneIdConverter进行反向查找
            return IanaToWindowsConverter.getWindowsIdForIana(ianaTimeZoneId);
        } catch (Exception e) {
            // 如果转换失败，记录日志并返回空字符串
            System.err.println("Failed to convert IANA timezone '" + ianaTimeZoneId + "' to Windows timezone: " + e.getMessage());
            return "";
        }
    }
}
